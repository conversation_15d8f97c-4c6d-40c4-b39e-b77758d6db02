#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB查询构建器

提供MongoDB查询的构建和优化功能：
- 链式查询构建
- 查询优化
- 时间范围查询
- 复杂条件组合

作者: User-DF Team
版本: 2.0.0
"""

from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, date
import re
from dataclasses import dataclass

from ...core import Logger, DataValidator, DataValidationException, ErrorCode





class MongoDBQueryBuilder:
    """MongoDB查询构建器"""
    
    def __init__(self):
        """初始化查询构建器"""
        self.logger = Logger.get_logger("MongoDBQueryBuilder")
        self._filter = {}
        self._projection = {}
        self._sort = []
        self._limit = None
        self._skip = None
        self._hint = None
        
    def reset(self) -> 'MongoDBQueryBuilder':
        """重置查询构建器"""
        self._filter = {}
        self._projection = {}
        self._sort = []
        self._limit = None
        self._skip = None
        self._hint = None
        return self
    
    def filter(self, **kwargs) -> 'MongoDBQueryBuilder':
        """
        添加过滤条件
        
        Args:
            **kwargs: 过滤条件
            
        Returns:
            查询构建器实例
        """
        for key, value in kwargs.items():
            self._filter[key] = value
        return self
    
    def filter_dict(self, filter_dict: Dict[str, Any]) -> 'MongoDBQueryBuilder':
        """
        添加过滤条件字典
        
        Args:
            filter_dict: 过滤条件字典
            
        Returns:
            查询构建器实例
        """
        self._filter.update(filter_dict)
        return self
    
    def equals(self, field: str, value: Any) -> 'MongoDBQueryBuilder':
        """
        等于条件
        
        Args:
            field: 字段名
            value: 值
            
        Returns:
            查询构建器实例
        """
        self._filter[field] = value
        return self
    
    def not_equals(self, field: str, value: Any) -> 'MongoDBQueryBuilder':
        """
        不等于条件
        
        Args:
            field: 字段名
            value: 值
            
        Returns:
            查询构建器实例
        """
        self._filter[field] = {"$ne": value}
        return self
    
    def greater_than(self, field: str, value: Any) -> 'MongoDBQueryBuilder':
        """
        大于条件
        
        Args:
            field: 字段名
            value: 值
            
        Returns:
            查询构建器实例
        """
        if field in self._filter and isinstance(self._filter[field], dict):
            self._filter[field]["$gt"] = value
        else:
            self._filter[field] = {"$gt": value}
        return self
    
    def greater_than_or_equal(self, field: str, value: Any) -> 'MongoDBQueryBuilder':
        """
        大于等于条件
        
        Args:
            field: 字段名
            value: 值
            
        Returns:
            查询构建器实例
        """
        if field in self._filter and isinstance(self._filter[field], dict):
            self._filter[field]["$gte"] = value
        else:
            self._filter[field] = {"$gte": value}
        return self
    
    def less_than(self, field: str, value: Any) -> 'MongoDBQueryBuilder':
        """
        小于条件
        
        Args:
            field: 字段名
            value: 值
            
        Returns:
            查询构建器实例
        """
        if field in self._filter and isinstance(self._filter[field], dict):
            self._filter[field]["$lt"] = value
        else:
            self._filter[field] = {"$lt": value}
        return self
    
    def less_than_or_equal(self, field: str, value: Any) -> 'MongoDBQueryBuilder':
        """
        小于等于条件
        
        Args:
            field: 字段名
            value: 值
            
        Returns:
            查询构建器实例
        """
        if field in self._filter and isinstance(self._filter[field], dict):
            self._filter[field]["$lte"] = value
        else:
            self._filter[field] = {"$lte": value}
        return self
    
    def in_values(self, field: str, values: List[Any]) -> 'MongoDBQueryBuilder':
        """
        包含条件
        
        Args:
            field: 字段名
            values: 值列表
            
        Returns:
            查询构建器实例
        """
        self._filter[field] = {"$in": values}
        return self
    
    def not_in_values(self, field: str, values: List[Any]) -> 'MongoDBQueryBuilder':
        """
        不包含条件
        
        Args:
            field: 字段名
            values: 值列表
            
        Returns:
            查询构建器实例
        """
        self._filter[field] = {"$nin": values}
        return self
    
    def exists(self, field: str, exists: bool = True) -> 'MongoDBQueryBuilder':
        """
        字段存在条件
        
        Args:
            field: 字段名
            exists: 是否存在
            
        Returns:
            查询构建器实例
        """
        self._filter[field] = {"$exists": exists}
        return self
    
    def regex(self, field: str, pattern: str, options: str = "") -> 'MongoDBQueryBuilder':
        """
        正则表达式条件
        
        Args:
            field: 字段名
            pattern: 正则表达式模式
            options: 正则表达式选项
            
        Returns:
            查询构建器实例
        """
        regex_dict = {"$regex": pattern}
        if options:
            regex_dict["$options"] = options
        self._filter[field] = regex_dict
        return self
    
    def text_search(self, text: str, language: Optional[str] = None) -> 'MongoDBQueryBuilder':
        """
        文本搜索
        
        Args:
            text: 搜索文本
            language: 语言
            
        Returns:
            查询构建器实例
        """
        text_dict = {"$search": text}
        if language:
            text_dict["$language"] = language
        self._filter["$text"] = text_dict
        return self
    
    def date_range(self, field: str, start_date: Union[str, datetime, date], 
                   end_date: Union[str, datetime, date]) -> 'MongoDBQueryBuilder':
        """
        日期范围条件
        
        Args:
            field: 字段名
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            查询构建器实例
        """
        # 转换日期格式
        start_days = self._convert_to_days(start_date)
        end_days = self._convert_to_days(end_date)
        
        self._filter[field] = {
            "$gte": start_days,
            "$lte": end_days
        }
        return self
    
    def uid_range(self, min_uid: int, max_uid: int) -> 'MongoDBQueryBuilder':
        """
        UID范围条件
        
        Args:
            min_uid: 最小UID
            max_uid: 最大UID
            
        Returns:
            查询构建器实例
        """
        # 验证UID范围
        DataValidator.validate(min_uid, 'uid', 'min_uid')
        DataValidator.validate(max_uid, 'uid', 'max_uid')
        
        if min_uid > max_uid:
            raise DataValidationException(
                f"最小UID {min_uid} 不能大于最大UID {max_uid}",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        self._filter["uid"] = {
            "$gte": min_uid,
            "$lte": max_uid
        }
        return self
    

    
    def and_conditions(self, *conditions: Dict[str, Any]) -> 'MongoDBQueryBuilder':
        """
        AND条件组合
        
        Args:
            *conditions: 条件列表
            
        Returns:
            查询构建器实例
        """
        if "$and" not in self._filter:
            self._filter["$and"] = []
        self._filter["$and"].extend(conditions)
        return self
    
    def or_conditions(self, *conditions: Dict[str, Any]) -> 'MongoDBQueryBuilder':
        """
        OR条件组合
        
        Args:
            *conditions: 条件列表
            
        Returns:
            查询构建器实例
        """
        if "$or" not in self._filter:
            self._filter["$or"] = []
        self._filter["$or"].extend(conditions)
        return self
    
    def project(self, **fields) -> 'MongoDBQueryBuilder':
        """
        设置投影字段
        
        Args:
            **fields: 字段投影设置
            
        Returns:
            查询构建器实例
        """
        self._projection.update(fields)
        return self
    
    def include_fields(self, *fields: str) -> 'MongoDBQueryBuilder':
        """
        包含字段
        
        Args:
            *fields: 字段名列表
            
        Returns:
            查询构建器实例
        """
        for field in fields:
            self._projection[field] = 1
        return self
    
    def exclude_fields(self, *fields: str) -> 'MongoDBQueryBuilder':
        """
        排除字段
        
        Args:
            *fields: 字段名列表
            
        Returns:
            查询构建器实例
        """
        for field in fields:
            self._projection[field] = 0
        return self
    
    def sort_by(self, field: str, direction: int = 1) -> 'MongoDBQueryBuilder':
        """
        添加排序条件
        
        Args:
            field: 字段名
            direction: 排序方向 (1: 升序, -1: 降序)
            
        Returns:
            查询构建器实例
        """
        self._sort.append((field, direction))
        return self
    
    def sort_ascending(self, field: str) -> 'MongoDBQueryBuilder':
        """
        升序排序
        
        Args:
            field: 字段名
            
        Returns:
            查询构建器实例
        """
        return self.sort_by(field, 1)
    
    def sort_descending(self, field: str) -> 'MongoDBQueryBuilder':
        """
        降序排序
        
        Args:
            field: 字段名
            
        Returns:
            查询构建器实例
        """
        return self.sort_by(field, -1)
    
    def limit_results(self, limit: int) -> 'MongoDBQueryBuilder':
        """
        限制结果数量
        
        Args:
            limit: 限制数量
            
        Returns:
            查询构建器实例
        """
        self._limit = limit
        return self
    
    def skip_results(self, skip: int) -> 'MongoDBQueryBuilder':
        """
        跳过结果数量
        
        Args:
            skip: 跳过数量
            
        Returns:
            查询构建器实例
        """
        self._skip = skip
        return self
    
    def hint_index(self, index: Union[str, List[Tuple[str, int]]]) -> 'MongoDBQueryBuilder':
        """
        设置索引提示
        
        Args:
            index: 索引名称或索引规范
            
        Returns:
            查询构建器实例
        """
        self._hint = index
        return self
    
    def _convert_to_days(self, date_value: Union[str, datetime, date]) -> int:
        """
        将日期转换为天数（自Unix纪元以来的天数）
        
        Args:
            date_value: 日期值
            
        Returns:
            天数
        """
        if isinstance(date_value, str):
            # 假设格式为YYYYMMDD
            if re.match(r'^\d{8}$', date_value):
                dt = datetime.strptime(date_value, '%Y%m%d')
            else:
                dt = datetime.fromisoformat(date_value)
        elif isinstance(date_value, date):
            dt = datetime.combine(date_value, datetime.min.time())
        elif isinstance(date_value, datetime):
            dt = date_value
        else:
            raise DataValidationException(
                f"不支持的日期格式: {type(date_value)}",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        # 计算自Unix纪元以来的天数
        epoch = datetime(1970, 1, 1)
        return (dt - epoch).days
    
    def build(self) -> Dict[str, Any]:
        """
        构建查询字典
        
        Returns:
            查询字典
        """
        query = {
            "filter": self._filter,
            "projection": self._projection if self._projection else None,
            "sort": self._sort if self._sort else None,
            "limit": self._limit,
            "skip": self._skip,
            "hint": self._hint
        }
        
        # 移除None值
        return {k: v for k, v in query.items() if v is not None}
    
    def get_filter(self) -> Dict[str, Any]:
        """获取过滤条件"""
        return self._filter.copy()
    
    def get_projection(self) -> Dict[str, Any]:
        """获取投影设置"""
        return self._projection.copy()
    
    def get_sort(self) -> List[Tuple[str, int]]:
        """获取排序设置"""
        return self._sort.copy()
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"MongoDBQuery(filter={self._filter}, projection={self._projection}, sort={self._sort})"
