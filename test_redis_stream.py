#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis Stream功能测试脚本

测试Redis Stream的基本功能，验证生产者和消费者是否正常工作
"""

import asyncio
import json
import time
from typing import Dict, Any
import redis.asyncio as redis

from shared.database.redis import RedisStreamProducer, RedisStreamConsumer, StreamConfig, ConsumerGroupConfig


async def test_redis_stream():
    """测试Redis Stream功能"""
    print("🧪 开始测试Redis Stream功能...")
    
    # 创建Redis客户端
    redis_client = redis.Redis(
        host="localhost",
        port=6379,
        db=0,
        decode_responses=True
    )
    
    try:
        # 测试Redis连接
        await redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False
    
    # 配置Stream
    stream_name = "test_stream"
    group_name = "test_group"
    consumer_name = "test_consumer"
    
    # 创建生产者
    stream_config = StreamConfig(
        stream_name=stream_name,
        max_length=1000,
        approximate_max_length=True
    )
    producer = RedisStreamProducer(redis_client, stream_config)
    
    # 创建消费者
    consumer_config = ConsumerGroupConfig(
        group_name=group_name,
        consumer_name=consumer_name,
        start_id="0",
        block_time=1000,
        count=5
    )
    consumer = RedisStreamConsumer(redis_client, stream_config, consumer_config)
    
    try:
        # 初始化消费者组
        await consumer.initialize()
        print("✅ 消费者组初始化成功")
        
        # 测试发送消息
        test_messages = [
            {"batch_id": "test_001", "users": [{"uid": 1, "pids": [101, 102]}], "service": "test"},
            {"batch_id": "test_002", "users": [{"uid": 2, "pids": [201, 202]}], "service": "test"},
            {"batch_id": "test_003", "users": [{"uid": 3, "pids": [301, 302]}], "service": "test"}
        ]
        
        print(f"📤 发送 {len(test_messages)} 条测试消息...")
        message_ids = []
        for msg in test_messages:
            message_id = await producer.send_message(msg)
            message_ids.append(message_id)
            print(f"  消息已发送: {msg['batch_id']} -> {message_id}")
        
        # 获取Stream信息
        stream_info = await producer.get_stream_info()
        print(f"📊 Stream状态: 长度={stream_info.get('length', 0)}")
        
        # 测试消费消息
        print("📥 开始消费消息...")
        received_messages = []
        
        async def message_callback(data: Dict[str, Any], message_id: str, fields: Dict[str, str]):
            """消息处理回调"""
            received_messages.append({
                "message_id": message_id,
                "data": data,
                "timestamp": fields.get("timestamp")
            })
            print(f"  收到消息: {data.get('batch_id', 'unknown')} (ID: {message_id})")
        
        # 创建消费任务
        consume_task = asyncio.create_task(
            consumer.consume_messages(message_callback, auto_ack=True)
        )
        
        # 等待消息处理
        await asyncio.sleep(3)
        
        # 停止消费
        await consumer.stop()
        consume_task.cancel()
        
        try:
            await consume_task
        except asyncio.CancelledError:
            pass
        
        # 验证结果
        print(f"✅ 消费完成，收到 {len(received_messages)} 条消息")
        
        if len(received_messages) == len(test_messages):
            print("✅ 消息数量匹配")
        else:
            print(f"⚠️  消息数量不匹配: 发送{len(test_messages)}, 接收{len(received_messages)}")
        
        # 验证消息内容
        for i, received in enumerate(received_messages):
            original = test_messages[i]
            if received["data"]["batch_id"] == original["batch_id"]:
                print(f"✅ 消息内容匹配: {original['batch_id']}")
            else:
                print(f"❌ 消息内容不匹配: 期望{original['batch_id']}, 实际{received['data']['batch_id']}")
        
        # 清理测试数据
        try:
            await redis_client.delete(stream_name)
            print("🧹 测试数据已清理")
        except:
            pass
        
        print("🎉 Redis Stream功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        await redis_client.aclose()


async def test_stream_error_handling():
    """测试Stream错误处理"""
    print("\n🧪 测试Stream错误处理...")
    
    redis_client = redis.Redis(
        host="localhost",
        port=6379,
        db=0,
        decode_responses=True
    )
    
    try:
        # 测试不存在的Stream
        stream_config = StreamConfig(stream_name="nonexistent_stream")
        producer = RedisStreamProducer(redis_client, stream_config)
        
        stream_info = await producer.get_stream_info()
        if stream_info.get("length", 0) == 0:
            print("✅ 不存在的Stream处理正常")
        
        # 测试消费者组创建
        consumer_config = ConsumerGroupConfig(
            group_name="test_error_group",
            consumer_name="test_error_consumer"
        )
        consumer = RedisStreamConsumer(redis_client, stream_config, consumer_config)
        
        await consumer.initialize()
        print("✅ 消费者组创建成功")
        
        # 重复创建消费者组（应该不报错）
        await consumer.initialize()
        print("✅ 重复创建消费者组处理正常")
        
        print("🎉 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False
    
    finally:
        await redis_client.aclose()


async def main():
    """主函数"""
    print("🚀 开始Redis Stream功能测试\n")
    
    # 基本功能测试
    basic_test_result = await test_redis_stream()
    
    # 错误处理测试
    error_test_result = await test_stream_error_handling()
    
    print(f"\n📋 测试结果:")
    print(f"  基本功能测试: {'✅ 通过' if basic_test_result else '❌ 失败'}")
    print(f"  错误处理测试: {'✅ 通过' if error_test_result else '❌ 失败'}")
    
    if basic_test_result and error_test_result:
        print("\n🎉 所有测试通过！Redis Stream功能正常")
        return True
    else:
        print("\n❌ 部分测试失败，请检查Redis Stream实现")
        return False


if __name__ == "__main__":
    asyncio.run(main())
