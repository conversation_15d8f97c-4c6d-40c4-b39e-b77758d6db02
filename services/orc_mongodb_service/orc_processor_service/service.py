#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理程序

独立的ORC文件处理程序，负责：
- 扫描和处理ORC文件
- 批量读取用户数据
- Milvus PID验证
- Redis队列数据发送
"""

import os
import sys
import asyncio
import json
import time
import glob
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import redis.asyncio as redis

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import Config<PERSON><PERSON><PERSON>, <PERSON><PERSON>, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.database.redis import RedisStreamProducer, StreamConfig
from shared.utils import DataProcessor
import pandas as pd
import pyarrow.orc as orc


# 移除FastAPI相关的请求模型，改为直接使用参数


@dataclass
class ORCFileInfo:
    """ORC文件信息"""
    file_path: str
    process_date: str
    prov_id: int
    file_size: int
    created_at: float


@dataclass
class UserBatch:
    """用户批次数据"""
    users: List[Dict[str, Any]]
    batch_id: str
    process_date: str
    prov_id: int
    total_users: int
    total_pids: int


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    total_users: int = 0
    processed_users: int = 0
    total_pids: int = 0
    valid_pids: int = 0
    processing_time: float = 0.0
    start_time: float = 0.0


class ORCProcessor:
    """ORC数据处理程序"""

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        # 使用新的统一配置结构
        self.config = config_manager.get_config("orc_processor", default={})
        self.global_config = config_manager.get_config("", default={})  # 获取根配置
        self.logger = Logger.get_logger(__name__)
        self.exception_handler = ExceptionHandler

        # 初始化组件
        self.data_processor = DataProcessor()
        self.milvus_pool = None
        self.milvus_operations = None
        self.redis_client = None
        self.stream_producer = None

        # 处理状态
        self.is_running = False
        self.is_processing = False
        self.stats = ProcessingStats(start_time=time.time())

        # 队列控制状态
        self.queue_paused = False
        self.queue_pause_start_time = None

        # 处理配置 - 从新的配置结构读取
        self.batch_size = self.config.get("batch_processing", {}).get("batch_size", 1000)
        self.pid_query_batch_size = self.config.get("batch_processing", {}).get("pid_query_batch_size", 15000)
        self.max_pids_per_user = self.config.get("max_pids_per_user", 300)
        self.enable_milvus_filtering = self.global_config.get("enable_milvus_filtering", True)
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化ORC数据处理微服务...")

            # 初始化Milvus连接池和操作类（仅在启用过滤时）
            if self.enable_milvus_filtering:
                try:
                    self.milvus_pool = MilvusPool(config_manager=self.config_manager)
                    milvus_config = self.global_config.get("milvus", {})
                    collections = milvus_config.get("collections", {})
                    content_collection = collections.get("content_collection", "content_tower_collection_20250616")
                    self.milvus_operations = MilvusVectorOperations(self.milvus_pool, content_collection)
                    self.logger.info(f"Milvus PID过滤已启用，使用集合: {content_collection}")
                except Exception as e:
                    self.logger.warning(f"Milvus初始化失败，将禁用PID过滤: {e}")
                    self.enable_milvus_filtering = False
                    self.milvus_pool = None
                    self.milvus_operations = None
            else:
                self.logger.info("Milvus PID过滤已禁用，跳过Milvus初始化")
                self.milvus_pool = None
                self.milvus_operations = None

            # 初始化Redis连接
            redis_config = self.global_config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )

            # 测试Redis连接
            await self.redis_client.ping()

            # 初始化Redis Stream生产者
            stream_config_dict = redis_config.get("stream", {})
            stream_config = StreamConfig(
                stream_name=stream_config_dict.get("stream_name", "mongodb_write_stream"),
                max_length=stream_config_dict.get("max_length"),
                approximate_max_length=stream_config_dict.get("approximate_max_length", True),
                max_retries=stream_config_dict.get("max_retries", 3),
                retry_delay=stream_config_dict.get("retry_delay", 1.0),
                message_ttl=stream_config_dict.get("message_ttl")
            )
            self.stream_producer = RedisStreamProducer(self.redis_client, stream_config)

            self.is_running = True
            self.logger.info("ORC数据处理微服务初始化完成")

        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭处理器"""
        try:
            self.logger.info("关闭ORC数据处理程序...")
            self.is_running = False

            if self.redis_client:
                await self.redis_client.close()

            if self.milvus_pool:
                await self.milvus_pool.close()

            self.logger.info("ORC数据处理程序已关闭")

        except Exception as e:
            self.logger.error(f"程序关闭失败: {e}")

    async def process_files(self, start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           province_ids: Optional[List[int]] = None):
        """处理ORC文件的主入口方法"""
        try:
            self.is_processing = True
            self.logger.info("开始处理ORC文件...")

            # 获取需要处理的ORC文件列表
            orc_files = await self._discover_orc_files(start_date, end_date, province_ids)

            if not orc_files:
                self.logger.info("没有找到需要处理的ORC文件")
                return

            self.stats.total_files = len(orc_files)
            self.logger.info(f"发现 {len(orc_files)} 个ORC文件需要处理")

            # 逐个处理ORC文件
            for orc_file_info in orc_files:
                if not self.is_processing:
                    self.logger.info("处理已被停止")
                    break

                await self._process_single_orc_file(orc_file_info)
                self.stats.processed_files += 1

                # 检查队列状态，必要时等待
                await self._wait_for_queue_space()

            self.stats.processing_time = time.time() - self.stats.start_time
            self.logger.info(f"处理完成，处理了 {self.stats.processed_files}/{self.stats.total_files} 个文件")

        except Exception as e:
            self.logger.error(f"处理失败: {e}")
            self.stats.failed_files += 1
            raise
        finally:
            self.is_processing = False
        
    async def _discover_orc_files(self, start_date: Optional[str] = None,
                                 end_date: Optional[str] = None,
                                 province_ids: Optional[List[int]] = None) -> List[ORCFileInfo]:
        """发现需要处理的ORC文件"""
        try:
            orc_files = []

            # 获取配置，优先使用传入参数
            if start_date is None:
                start_date = self.config.get("start_date", "20250629")

            if end_date is None:
                end_date = self.config.get("end_date", "20250630")

            if province_ids is None:
                province_ids = self.config.get("province_ids", [200])

            orc_base_path = self.config.get("orc_base_path", "/workdir/hive_data/tw_user_pic_daily_aggregation")
            orc_file_pattern = self.config.get("orc_file_pattern", "*")

            # 处理空province_ids的情况 - 自动发现所有可用省份
            if not province_ids:  # 空数组或None
                self.logger.info("province_ids为空，自动发现所有可用省份...")
                province_ids = self._discover_available_provinces(orc_base_path)
                if not province_ids:
                    self.logger.warning("未发现任何可用省份目录")
                    return []
                self.logger.info(f"发现可用省份: {province_ids}")

            self.logger.info(f"扫描ORC文件: 日期范围 {start_date}-{end_date}, 省份 {province_ids}")
            if start_date != self.config.get("start_date") or end_date != self.config.get("end_date") or province_ids != self.config.get("province_ids"):
                self.logger.info("使用命令行参数覆盖配置文件设置")

            # 生成日期范围
            current_date = datetime.strptime(start_date, "%Y%m%d")
            end_date_obj = datetime.strptime(end_date, "%Y%m%d")

            while current_date <= end_date_obj:
                date_str = current_date.strftime("%Y%m%d")

                for prov_id in province_ids:
                    # 构建文件路径模式
                    file_pattern = os.path.join(
                        orc_base_path,
                        f"prov_id={prov_id}",
                        f"statis_ymd={date_str}",
                        orc_file_pattern
                    )

                    # 查找匹配的文件
                    matching_files = glob.glob(file_pattern)

                    for file_path in matching_files:
                        if os.path.isfile(file_path):
                            file_info = ORCFileInfo(
                                file_path=file_path,
                                process_date=date_str,
                                prov_id=prov_id,
                                file_size=os.path.getsize(file_path),
                                created_at=time.time()
                            )
                            orc_files.append(file_info)
                            self.logger.debug(f"发现ORC文件: {file_path}")

                current_date += timedelta(days=1)

            self.logger.info(f"总共发现 {len(orc_files)} 个ORC文件")
            return orc_files

        except Exception as e:
            self.logger.error(f"发现ORC文件失败: {e}")
            return []

    def _discover_available_provinces(self, orc_base_path: str) -> List[int]:
        """发现可用的省份目录"""
        try:
            province_ids = []
            if not os.path.exists(orc_base_path):
                self.logger.warning(f"ORC基础路径不存在: {orc_base_path}")
                return province_ids

            # 扫描prov_id=*格式的目录
            for item in os.listdir(orc_base_path):
                item_path = os.path.join(orc_base_path, item)
                if os.path.isdir(item_path) and item.startswith("prov_id="):
                    try:
                        prov_id = int(item.split("=")[1])
                        province_ids.append(prov_id)
                    except (ValueError, IndexError):
                        self.logger.debug(f"跳过无效的省份目录: {item}")
                        continue

            province_ids.sort()  # 排序以保持一致性
            self.logger.debug(f"发现 {len(province_ids)} 个省份目录: {province_ids}")
            return province_ids

        except Exception as e:
            self.logger.error(f"发现可用省份失败: {e}")
            return []
    
    async def _process_single_orc_file(self, orc_file_info: ORCFileInfo):
        """处理单个ORC文件"""
        try:
            self.logger.info(f"开始处理ORC文件: {orc_file_info.file_path}")
            file_start_time = time.time()

            # 读取ORC文件
            df = self._read_orc_file(orc_file_info.file_path)
            total_rows = len(df)

            if total_rows == 0:
                self.logger.warning(f"ORC文件为空: {orc_file_info.file_path}")
                return

            self.logger.info(f"ORC文件包含 {total_rows} 行数据")

            # 分批处理用户数据
            processed_users = 0
            for batch_start in range(0, total_rows, self.batch_size):
                if not self.is_processing:
                    break

                batch_end = min(batch_start + self.batch_size, total_rows)
                batch_df = df.iloc[batch_start:batch_end]

                # 处理当前批次
                user_batch = await self._process_user_batch(
                    batch_df,
                    orc_file_info.process_date,
                    orc_file_info.prov_id,
                    f"batch_{batch_start}_{batch_end}"
                )

                if user_batch and user_batch.users:
                    # 发送到Redis队列
                    await self._send_batch_to_queue(user_batch)
                    processed_users += len(user_batch.users)
                    self.stats.processed_users += len(user_batch.users)

                # 检查队列状态
                await self._wait_for_queue_space()

            processing_time = time.time() - file_start_time
            self.logger.info(f"ORC文件处理完成: {orc_file_info.file_path}, "
                           f"处理用户数: {processed_users}/{total_rows}, "
                           f"耗时: {processing_time:.2f}秒")

        except Exception as e:
            self.logger.error(f"处理ORC文件失败: {orc_file_info.file_path}, 错误: {e}")
            self.stats.failed_files += 1
            raise

    async def _process_user_batch(self, batch_df: pd.DataFrame, process_date: str,
                                 prov_id: int, batch_id: str) -> Optional[UserBatch]:
        """处理用户批次数据"""
        try:
            users = []
            total_pids = 0

            # 获取列名映射配置
            column_mapping = self.config.get("column_mapping", {})
            uid_columns = column_mapping.get("uid_columns", ["id", "uid", "user_id"])
            pid_columns = column_mapping.get("pid_columns", ["pic_id_list", "pid_list", "pid"])

            # 处理每个用户
            for _, row in batch_df.iterrows():
                # 提取UID
                uid = None
                for col in uid_columns:
                    if col in row and pd.notna(row[col]):
                        uid = int(row[col])
                        break

                if uid is None:
                    continue

                # 提取PID列表
                pid_list = []
                for col in pid_columns:
                    if col in row and pd.notna(row[col]):
                        pids = row[col]
                        if isinstance(pids, str):
                            # 如果是字符串，尝试解析
                            try:
                                if pids.startswith('[') and pids.endswith(']'):
                                    pid_list = json.loads(pids)
                                else:
                                    pid_list = [int(x.strip()) for x in pids.split(',') if x.strip()]
                            except:
                                continue
                        elif isinstance(pids, (list, tuple)):
                            pid_list = [int(p) for p in pids if pd.notna(p)]
                        elif isinstance(pids, (int, float)):
                            pid_list = [int(pids)]
                        break

                if not pid_list:
                    continue

                # 限制PID数量
                if len(pid_list) > self.max_pids_per_user:
                    pid_list = pid_list[:self.max_pids_per_user]

                total_pids += len(pid_list)

                # 计算updated_days（从Unix纪元开始的天数）
                current_timestamp = int(time.time())
                updated_days = current_timestamp // 86400  # 86400秒 = 1天

                user_data = {
                    "_id": uid,  # 使用用户ID作为_id字段
                    "pid_list": pid_list,  # 保持一致的字段名
                    "pid_count": len(pid_list),
                    "updated_days": updated_days,
                    "prov_id": prov_id  # 保留省份ID用于确定集合
                }
                users.append(user_data)

            if not users:
                return None

            # Milvus PID过滤
            if self.enable_milvus_filtering and self.milvus_operations:
                users = await self._filter_users_by_milvus(users)

            if not users:
                return None

            user_batch = UserBatch(
                users=users,
                batch_id=batch_id,
                process_date=process_date,
                prov_id=prov_id,
                total_users=len(users),
                total_pids=sum(len(u["pid_list"]) for u in users)
            )

            self.logger.debug(f"批次处理完成: {batch_id}, 用户数: {len(users)}, PID数: {user_batch.total_pids}")
            return user_batch

        except Exception as e:
            self.logger.error(f"处理用户批次失败: {batch_id}, 错误: {e}")
            return None

    async def _filter_users_by_milvus(self, users: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """通过Milvus过滤用户的PID"""
        try:
            # 收集所有PID
            all_pids = []
            for user in users:
                all_pids.extend(user["pid_list"])

            if not all_pids:
                return users

            # 去重PID
            unique_pids = list(set(all_pids))
            self.logger.debug(f"开始Milvus PID过滤，总PID数: {len(unique_pids)}")

            # 分批查询Milvus
            valid_pids = set()
            for batch_start in range(0, len(unique_pids), self.pid_query_batch_size):
                batch_end = min(batch_start + self.pid_query_batch_size, len(unique_pids))
                pid_batch = unique_pids[batch_start:batch_end]

                # 查询Milvus
                result = self.milvus_operations.query_vectors(
                    ids=pid_batch,
                    output_fields=["item_id"]
                )

                if result.success and result.results:
                    batch_valid_pids = {r.get("item_id") for r in result.results if r.get("item_id")}
                    valid_pids.update(batch_valid_pids)

            self.logger.debug(f"Milvus查询完成，有效PID数: {len(valid_pids)}/{len(unique_pids)}")
            self.stats.total_pids += len(unique_pids)
            self.stats.valid_pids += len(valid_pids)

            # 过滤用户的PID
            filtered_users = []
            for user in users:
                filtered_pids = [pid for pid in user["pid_list"] if pid in valid_pids]
                if filtered_pids:
                    user["pid_list"] = filtered_pids
                    filtered_users.append(user)

            self.logger.debug(f"PID过滤完成，保留用户数: {len(filtered_users)}/{len(users)}")
            return filtered_users

        except Exception as e:
            self.logger.error(f"Milvus PID过滤失败: {e}")
            # 出错时返回原始用户数据
            return users

    def _read_orc_file(self, file_path: str) -> pd.DataFrame:
        """读取ORC文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")

            orc_file = orc.ORCFile(file_path)
            table = orc_file.read()
            df = table.to_pandas()

            self.logger.debug(f"成功读取ORC文件: {file_path}, 行数: {len(df)}")
            return df

        except Exception as e:
            self.logger.error(f"读取ORC文件失败: {file_path}, 错误: {e}")
            raise

    async def _send_batch_to_queue(self, user_batch: UserBatch):
        """发送用户批次到Redis Stream"""
        try:
            # 准备消息数据
            message_data = asdict(user_batch)
            message_data["service"] = "orc_processor_service"

            # 发送到Redis Stream
            message_id = await self.stream_producer.send_message(message_data)

            self.logger.debug(f"批次数据已发送到Stream: {user_batch.batch_id}, 用户数: {user_batch.total_users}, 消息ID: {message_id}")

        except Exception as e:
            self.logger.error(f"发送批次到Stream失败: {e}")
            raise

    async def _check_queue_length(self) -> bool:
        """
        检查Redis Stream长度，决定是否需要暂停处理

        Returns:
            bool: True表示可以继续处理，False表示需要暂停
        """
        try:
            redis_config = self.global_config.get("redis", {})
            stream_name = redis_config.get("stream", {}).get("stream_name", "mongodb_write_stream")
            queue_control = redis_config.get("queue_control", {})

            # 获取Stream长度
            stream_info = await self.stream_producer.get_stream_info()
            queue_length = stream_info.get("length", 0)

            # 获取配置参数
            pause_threshold = queue_control.get("pause_threshold", 8000)
            resume_threshold = queue_control.get("resume_threshold", 5000)

            current_time = time.time()

            # 检查是否需要暂停
            if not self.queue_paused and queue_length >= pause_threshold:
                self.queue_paused = True
                self.queue_pause_start_time = current_time
                self.logger.warning(f"Redis Stream长度达到暂停阈值 {pause_threshold}，当前长度: {queue_length}，暂停ORC处理")
                return False

            # 检查是否可以恢复
            elif self.queue_paused:
                # 只根据恢复阈值决定是否恢复
                if queue_length <= resume_threshold:
                    self.queue_paused = False
                    pause_duration = current_time - (self.queue_pause_start_time or current_time)
                    self.queue_pause_start_time = None
                    self.logger.info(f"Redis Stream长度降至恢复阈值 {resume_threshold}，当前长度: {queue_length}，恢复ORC处理，暂停时长: {pause_duration:.1f}秒")
                    return True
                else:
                    # 仍需要等待，直到Stream长度降到恢复阈值以下
                    wait_time = current_time - (self.queue_pause_start_time or current_time)
                    self.logger.debug(f"Stream仍在暂停中，当前长度: {queue_length}，已等待: {wait_time:.1f}秒")
                    return False

            # 正常状态，可以继续处理
            return True

        except Exception as e:
            self.logger.error(f"检查Stream长度失败: {e}")
            # 出错时允许继续处理，避免阻塞
            return True

    async def _wait_for_queue_space(self):
        """等待队列有空间可用"""
        redis_config = self.global_config.get("redis", {})
        queue_control = redis_config.get("queue_control", {})
        check_interval = queue_control.get("check_interval", 5)

        while self.is_running:
            if await self._check_queue_length():
                break

            # 等待一段时间后再检查
            await asyncio.sleep(check_interval)

    def get_processing_status(self) -> Dict[str, Any]:
        """获取处理状态"""
        return {
            "is_running": self.is_running,
            "is_processing": self.is_processing,
            "queue_paused": self.queue_paused,
            "stats": asdict(self.stats),
            "config": {
                "batch_size": self.batch_size,
                "pid_query_batch_size": self.pid_query_batch_size,
                "max_pids_per_user": self.max_pids_per_user,
                "enable_milvus_filtering": self.enable_milvus_filtering
            }
        }


async def create_processor():
    """创建处理器实例"""
    config_manager = ConfigManager()
    processor = ORCProcessor(config_manager)
    await processor.initialize()
    return processor
