#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务状态检查脚本
快速检查服务状态的独立脚本
"""

import os
import sys
import asyncio
import json
import aiohttp
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


async def check_service_health(url: str, service_name: str) -> Dict[str, Any]:
    """检查单个服务健康状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "name": service_name,
                        "status": "healthy",
                        "uptime": data.get("uptime", 0),
                        "version": data.get("version", "unknown"),
                        "url": url
                    }
                else:
                    return {
                        "name": service_name,
                        "status": "unhealthy",
                        "error": f"HTTP {response.status}",
                        "url": url
                    }
    except Exception as e:
        return {
            "name": service_name,
            "status": "error",
            "error": str(e),
            "url": url
        }


async def check_redis_stream(config: Dict[str, Any]) -> Dict[str, Any]:
    """检查Redis Stream状态"""
    try:
        import redis.asyncio as redis

        redis_config = config.get("redis", {})
        redis_client = redis.Redis(
            host=redis_config.get("host", "localhost"),
            port=redis_config.get("port", 6379),
            db=redis_config.get("db", 0),
            decode_responses=True
        )

        # 检查Stream状态
        stream_config = redis_config.get("stream", {})
        stream_name = stream_config.get("stream_name", "mongodb_write_stream")

        try:
            # 获取Stream信息
            stream_info = await redis_client.xinfo_stream(stream_name)
            stream_length = stream_info.get("length", 0)

            # 获取消费者组信息
            consumer_group_config = stream_config.get("consumer_group", {})
            group_name = consumer_group_config.get("group_name", "mongodb_writers")

            try:
                groups_info = await redis_client.xinfo_groups(stream_name)
                group_info = next((g for g in groups_info if g["name"] == group_name), None)
            except:
                group_info = None

            await redis_client.aclose()

            return {
                "stream_name": stream_name,
                "stream_length": stream_length,
                "group_name": group_name,
                "group_info": group_info,
                "status": "healthy"
            }

        except redis.ResponseError as e:
            if "no such key" in str(e).lower():
                # Stream不存在
                await redis_client.aclose()
                return {
                    "stream_name": stream_name,
                    "stream_length": 0,
                    "group_name": group_name,
                    "group_info": None,
                    "status": "healthy",
                    "note": "Stream not created yet"
                }
            else:
                raise

    except Exception as e:
        return {
            "stream_name": "unknown",
            "stream_length": -1,
            "status": "error",
            "error": str(e)
        }

async def check_redis_queue(config: Dict[str, Any]) -> Dict[str, Any]:
    """检查Redis队列状态（保留兼容性）"""
    return await check_redis_stream(config)


async def main():
    """主函数"""
    try:
        # 初始化配置
        config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")
        config = config_manager.get_config("orc_mongodb_service")
        logger = Logger.get_logger(__name__)
        
        print("=== ORC MongoDB服务状态检查 ===\n")
        
        # 检查服务状态
        services_to_check = [
            ("MongoDB写入服务", f"http://localhost:{config.get('mongodb_writer_service', {}).get('port', 8002)}")
        ]
        
        service_results = []
        for service_name, service_url in services_to_check:
            result = await check_service_health(service_url, service_name)
            service_results.append(result)
        
        # 检查Redis队列
        queue_status = await check_redis_queue(config)
        
        # 显示结果
        print("📊 服务状态:")
        for result in service_results:
            status_icon = "✅" if result["status"] == "healthy" else "❌"
            print(f"  {status_icon} {result['name']}: {result['status']}")
            if result["status"] == "healthy":
                print(f"     运行时间: {result.get('uptime', 0):.1f}秒")
                print(f"     版本: {result.get('version', 'unknown')}")
            elif "error" in result:
                print(f"     错误: {result['error']}")
            print(f"     URL: {result['url']}")
            print()
        
        print("🔄 Stream状态:")
        queue_icon = "✅" if queue_status["status"] == "healthy" else "❌"
        print(f"  {queue_icon} Redis Stream: {queue_status['status']}")
        print(f"     Stream名称: {queue_status.get('stream_name', 'unknown')}")
        print(f"     Stream长度: {queue_status.get('stream_length', -1)}")
        print(f"     消费者组: {queue_status.get('group_name', 'unknown')}")

        if queue_status.get("group_info"):
            group_info = queue_status["group_info"]
            print(f"     消费者数量: {group_info.get('consumers', 0)}")
            print(f"     待处理消息: {group_info.get('pending', 0)}")

        if "note" in queue_status:
            print(f"     备注: {queue_status['note']}")

        if "error" in queue_status:
            print(f"     错误: {queue_status['error']}")
        print()

        # Stream长度警告
        stream_length = queue_status.get("stream_length", 0)
        if stream_length > 0:
            queue_control = config.get("redis", {}).get("queue_control", {})
            pause_threshold = queue_control.get("pause_threshold", 150)
            resume_threshold = queue_control.get("resume_threshold", 20)
            
            if stream_length >= pause_threshold:
                print(f"⚠️  警告: Stream长度 ({stream_length}) 超过暂停阈值 ({pause_threshold})")
                print("   ORC处理可能已暂停")
            elif stream_length > resume_threshold:
                print(f"ℹ️  信息: Stream长度 ({stream_length}) 高于恢复阈值 ({resume_threshold})")
                print("   Stream处理正常，但需要关注")
            print()
        
        # 配置信息
        global_config = config_manager.get_config("global")
        print("⚙️  配置信息:")
        print(f"  环境: {global_config.get('project', {}).get('environment', 'unknown')}")
        print(f"  版本: {global_config.get('project', {}).get('version', 'unknown')}")
        print(f"  Redis: {config.get('redis', {}).get('host', 'localhost')}:{config.get('redis', {}).get('port', 6379)}")
        mongodb_config = config_manager.get_config("mongodb")
        print(f"  MongoDB: {mongodb_config.get('connection', {}).get('host', 'localhost')}:{mongodb_config.get('connection', {}).get('port', 27017)}")
        
        # 判断整体状态
        all_healthy = all(result["status"] == "healthy" for result in service_results) and queue_status["status"] == "healthy"
        
        print("\n" + "="*50)
        if all_healthy:
            print("✅ 所有服务运行正常")
            sys.exit(0)
        else:
            print("❌ 部分服务存在问题，请检查上述状态")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
