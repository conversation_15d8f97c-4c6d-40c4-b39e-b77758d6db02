#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户向量服务监控服务

监控用户向量服务的三个微服务状态和性能
"""

import os
import sys
import asyncio
import json
import time
import argparse
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import aiohttp
import redis.asyncio as redis
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger


@dataclass
class ServiceHealth:
    """服务健康状态"""
    name: str
    status: str
    uptime: float
    version: str
    url: str
    response_time: float
    error_message: Optional[str] = None


class UserVectorMonitoringService:
    """用户向量服务监控服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_service_config("user_vector_monitoring_service")
        self.logger = Logger(__name__, config_manager)
        
        # 服务配置
        self.services = self.config.get("services", {})
        
        # 监控状态
        self.service_status = {}
        self.is_running = False
        self.console = Console()
        self.redis_client = None
        
        # 监控统计
        self.monitor_stats = {
            "start_time": time.time(),
            "total_checks": 0,
            "failed_checks": 0,
            "alerts_sent": 0
        }
    
    async def initialize(self):
        """初始化监控服务"""
        try:
            self.logger.info("初始化用户向量服务监控...")
            
            # 初始化Redis连接（用于获取队列状态）
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            self.logger.info("用户向量服务监控初始化完成")
            
        except Exception as e:
            self.logger.error(f"监控服务初始化失败: {e}")
            raise
    
    async def check_service_health(self, service_key: str, service_config: Dict[str, Any]) -> ServiceHealth:
        """检查单个服务健康状态"""
        service_name = service_config.get("name", service_key)
        service_url = service_config.get("url", "")
        health_endpoint = service_config.get("health_endpoint", "/health")
        
        start_time = time.time()
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.config.get("monitoring", {}).get("request_timeout", 5))
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(f"{service_url}{health_endpoint}") as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        return ServiceHealth(
                            name=service_name,
                            status="healthy",
                            uptime=data.get("uptime", 0),
                            version=data.get("version", "unknown"),
                            url=service_url,
                            response_time=response_time
                        )
                    else:
                        return ServiceHealth(
                            name=service_name,
                            status="unhealthy",
                            uptime=0,
                            version="unknown",
                            url=service_url,
                            response_time=response_time,
                            error_message=f"HTTP {response.status}"
                        )
        
        except Exception as e:
            response_time = time.time() - start_time
            return ServiceHealth(
                name=service_name,
                status="error",
                uptime=0,
                version="unknown",
                url=service_url,
                response_time=response_time,
                error_message=str(e)
            )
    
    async def get_service_stats(self, service_key: str, service_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取服务统计信息"""
        service_url = service_config.get("url", "")
        stats_endpoint = service_config.get("stats_endpoint", "/stats")
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.config.get("monitoring", {}).get("request_timeout", 5))
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(f"{service_url}{stats_endpoint}") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        
        except Exception as e:
            return {"error": str(e)}
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取Redis Stream状态"""
        try:
            redis_config = self.config.get("redis", {})

            # 获取Stream配置
            vector_stream_config = redis_config.get("input_stream", {})
            storage_stream_config = redis_config.get("output_stream", {})

            vector_stream_name = vector_stream_config.get("stream_name", "vector_processing_stream")
            storage_stream_name = storage_stream_config.get("stream_name", "vector_storage_stream")

            # 获取Stream长度
            try:
                vector_stream_info = await self.redis_client.xinfo_stream(vector_stream_name)
                vector_stream_length = vector_stream_info.get("length", 0)
            except:
                vector_stream_length = 0

            try:
                storage_stream_info = await self.redis_client.xinfo_stream(storage_stream_name)
                storage_stream_length = storage_stream_info.get("length", 0)
            except:
                storage_stream_length = 0

            return {
                "vector_processing_stream": {
                    "name": vector_stream_name,
                    "length": vector_stream_length
                },
                "vector_storage_stream": {
                    "name": storage_stream_name,
                    "length": storage_stream_length
                },
                "status": "healthy"
            }

        except Exception as e:
            return {
                "vector_processing_stream": {"name": "unknown", "length": -1},
                "vector_storage_stream": {"name": "unknown", "length": -1},
                "status": "error",
                "error": str(e)
            }
    
    async def collect_all_stats(self) -> Dict[str, Any]:
        """收集所有服务的统计信息"""
        all_stats = {
            "timestamp": time.time(),
            "services": {},
            "queues": {},
            "monitor": self.monitor_stats
        }
        
        # 检查所有服务
        for service_key, service_config in self.services.items():
            # 健康检查
            health_status = await self.check_service_health(service_key, service_config)
            self.service_status[service_key] = health_status
            
            # 详细统计
            stats = await self.get_service_stats(service_key, service_config)
            
            all_stats["services"][service_key] = {
                "health": asdict(health_status),
                "stats": stats
            }
            
            self.monitor_stats["total_checks"] += 1
            if health_status.status != "healthy":
                self.monitor_stats["failed_checks"] += 1
        
        # 队列状态
        all_stats["queues"] = await self.get_queue_status()
        
        return all_stats
    
    def create_monitoring_display(self, stats: Dict[str, Any]) -> Layout:
        """创建监控显示界面"""
        layout = Layout()
        
        # 创建主要布局
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # 头部信息
        header_text = Text("用户向量服务监控面板", style="bold blue")
        header_text.append(f" - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim")
        layout["header"].update(Panel(header_text, title="监控状态"))
        
        # 主体分为左右两部分
        layout["body"].split_row(
            Layout(name="services", ratio=2),
            Layout(name="details", ratio=1)
        )
        
        # 服务状态表格
        services_table = Table(title="微服务状态")
        services_table.add_column("服务名称", style="cyan")
        services_table.add_column("状态", style="green")
        services_table.add_column("运行时间", style="yellow")
        services_table.add_column("响应时间", style="magenta")
        services_table.add_column("版本", style="blue")
        
        for service_key, service_data in stats["services"].items():
            health = service_data["health"]
            service_stats = service_data["stats"]
            
            status_style = "green" if health["status"] == "healthy" else "red"
            status_text = f"[{status_style}]{health['status']}[/{status_style}]"
            
            uptime_text = self._format_uptime(health["uptime"])
            response_time_text = f"{health['response_time']:.3f}s"
            
            services_table.add_row(
                health["name"],
                status_text,
                uptime_text,
                response_time_text,
                health["version"]
            )
        
        layout["services"].update(Panel(services_table, title="服务状态"))
        
        # 详细信息面板
        details_text = []
        
        # Stream状态
        queue_info = stats["queues"]
        details_text.append("[bold cyan]Stream状态:[/bold cyan]")

        if "vector_processing_stream" in queue_info:
            vp_stream = queue_info["vector_processing_stream"]
            details_text.append(f"向量处理Stream: {vp_stream['length']}")

        if "vector_storage_stream" in queue_info:
            vs_stream = queue_info["vector_storage_stream"]
            details_text.append(f"向量存储Stream: {vs_stream['length']}")
        
        details_text.append("")
        details_text.append("[bold cyan]统计信息:[/bold cyan]")
        
        # 汇总统计
        total_processed = 0
        total_stored = 0
        total_failed = 0
        
        for service_key, service_data in stats["services"].items():
            service_stats = service_data["stats"].get("stats", {})
            
            if "total_batches_processed" in service_stats:
                total_processed += service_stats["total_batches_processed"]
            if "total_vectors_stored" in service_stats:
                total_stored += service_stats["total_vectors_stored"]
            if "failed_batches" in service_stats:
                total_failed += service_stats["failed_batches"]
        
        details_text.append(f"总处理批次: {total_processed}")
        details_text.append(f"总存储向量: {total_stored}")
        details_text.append(f"总失败批次: {total_failed}")
        details_text.append(f"监控检查: {self.monitor_stats['total_checks']}")
        details_text.append(f"检查失败: {self.monitor_stats['failed_checks']}")
        
        details_panel = Panel("\n".join(details_text), title="详细信息")
        layout["details"].update(details_panel)
        
        # 底部信息
        footer_text = f"监控运行时间: {self._format_uptime(time.time() - self.monitor_stats['start_time'])}"
        layout["footer"].update(Panel(footer_text, style="dim"))
        
        return layout
    
    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"
    
    async def run_monitoring_loop(self):
        """运行监控循环"""
        check_interval = self.config.get("monitoring", {}).get("check_interval", 5)
        
        with Live(console=self.console, refresh_per_second=1) as live:
            while self.is_running:
                try:
                    # 收集统计信息
                    stats = await self.collect_all_stats()
                    
                    # 更新显示
                    display = self.create_monitoring_display(stats)
                    live.update(display)
                    
                    # 等待下次检查
                    await asyncio.sleep(check_interval)
                    
                except KeyboardInterrupt:
                    self.logger.info("接收到中断信号，停止监控")
                    break
                except Exception as e:
                    self.logger.error(f"监控循环错误: {e}")
                    await asyncio.sleep(1)
    
    async def run_once(self) -> Dict[str, Any]:
        """运行一次监控检查"""
        return await self.collect_all_stats()
    
    def print_stats_summary(self, stats: Dict[str, Any]):
        """打印统计摘要"""
        self.console.print("\n=== 用户向量服务状态摘要 ===", style="bold blue")
        
        for service_key, service_data in stats["services"].items():
            health = service_data["health"]
            service_stats = service_data["stats"].get("stats", {})
            
            status_style = "green" if health["status"] == "healthy" else "red"
            
            self.console.print(f"\n[cyan]{health['name']}[/cyan]:")
            self.console.print(f"  状态: [{status_style}]{health['status']}[/{status_style}]")
            self.console.print(f"  运行时间: {self._format_uptime(health['uptime'])}")
            self.console.print(f"  响应时间: {health['response_time']:.3f}s")
            
            if "total_batches_processed" in service_stats:
                self.console.print(f"  处理批次: {service_stats['total_batches_processed']}")
            if "total_vectors_stored" in service_stats:
                self.console.print(f"  存储向量: {service_stats['total_vectors_stored']}")
            if "failed_batches" in service_stats:
                self.console.print(f"  失败批次: {service_stats['failed_batches']}")
            
            if health.get("error_message"):
                self.console.print(f"  错误: [red]{health['error_message']}[/red]")
        
        # Stream状态
        queue_info = stats["queues"]
        self.console.print(f"\n[cyan]消息Stream[/cyan]:")

        if "vector_processing_stream" in queue_info:
            vp_stream = queue_info["vector_processing_stream"]
            self.console.print(f"  向量处理Stream: {vp_stream['length']}")

        if "vector_storage_stream" in queue_info:
            vs_stream = queue_info["vector_storage_stream"]
            self.console.print(f"  向量存储Stream: {vs_stream['length']}")


async def create_monitoring_service():
    """创建监控服务实例"""
    config_manager = ConfigManager()
    service = UserVectorMonitoringService(config_manager)
    await service.initialize()
    return service


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="用户向量服务监控")
    parser.add_argument("--mode", choices=["live", "once"], default="live", help="监控模式")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        if args.config:
            config_manager.load_config_file(args.config)
        else:
            # 使用默认配置
            config_manager.load_service_config("user_vector_monitoring_service")
        
        # 创建监控服务
        service = UserVectorMonitoringService(config_manager)
        await service.initialize()
        
        if args.mode == "live":
            # 实时监控模式
            await service.run_monitoring_loop()
        else:
            # 单次检查模式
            stats = await service.run_once()
            service.print_stats_summary(stats)
        
    except Exception as e:
        print(f"监控服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
