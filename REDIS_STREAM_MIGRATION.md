# Redis Stream 迁移文档

## 概述

本文档记录了将 `orc_mongodb_service` 和 `user_vector_service` 从 Redis List 队列迁移到 Redis Stream 的完整过程。

## 迁移目标

- 将现有的 Redis List (LPUSH/BRPOP) 消息队列替换为 Redis Stream
- 保持所有现有功能逻辑不变
- 提供更好的消息持久性、重试机制和监控能力
- 支持消费者组和消息确认机制

## 架构变更

### Redis Stream 命名规则

| 服务 | 原队列名称 | 新Stream名称 |
|------|------------|--------------|
| orc_mongodb_service | mongodb_write_queue | mongodb_write_stream |
| user_vector_service | vector_processing_queue | vector_processing_stream |
| user_vector_service | vector_storage_queue | vector_storage_stream |

### 消费者组设计

| Stream | 消费者组 | 消费者名称 |
|--------|----------|------------|
| mongodb_write_stream | mongodb_writers | writer_001 |
| vector_processing_stream | vector_processors | processor_001 |
| vector_storage_stream | vector_writers | writer_001 |

## 核心组件

### 1. Redis Stream 工具类

**文件**: `shared/database/redis/stream_operations.py`

新增了以下核心类：
- `StreamConfig`: Stream配置类
- `ConsumerGroupConfig`: 消费者组配置类  
- `RedisStreamProducer`: Stream生产者
- `RedisStreamConsumer`: Stream消费者

**主要功能**:
- 消息生产和批量生产
- 消费者组管理
- 消息消费和确认
- 待处理消息重试机制
- Stream信息查询

### 2. 配置文件更新

所有相关配置文件都已更新，添加了 Redis Stream 配置段：

```yaml
redis:
  # 保留原有队列配置（兼容性）
  queue_name: "mongodb_write_queue"
  
  # 新增Stream配置
  stream:
    stream_name: "mongodb_write_stream"
    max_length: 10000
    approximate_max_length: true
    message_ttl: 3600
    
    consumer_group:
      group_name: "mongodb_writers"
      consumer_name: "writer_001"
      start_id: "0"
      block_time: 1000
      count: 10
      pending_timeout: 300
      max_delivery_count: 3
```

## 服务修改详情

### orc_mongodb_service

#### orc_processor_service (生产者)
- **文件**: `services/orc_mongodb_service/orc_processor_service/service.py`
- **主要变更**:
  - 添加 `RedisStreamProducer` 初始化
  - 修改 `_send_batch_to_queue()` 方法使用Stream发送
  - 更新 `_check_queue_length()` 方法检查Stream长度

#### mongodb_writer_service (消费者)
- **文件**: `services/orc_mongodb_service/mongodb_writer_service/service.py`
- **主要变更**:
  - 添加 `RedisStreamConsumer` 初始化
  - 替换 `_process_queue()` 为 `_process_stream()`
  - 实现单消息处理逻辑
  - 更新队列状态API返回Stream信息

### user_vector_service

#### mongodb_reader_service (生产者)
- **文件**: `services/user_vector_service/mongodb_reader_service/service.py`
- **主要变更**:
  - 添加 `RedisStreamProducer` 初始化
  - 修改 `_send_to_queue()` 方法使用Stream发送
  - 更新队列长度检查逻辑

#### vector_processor_service (消费者+生产者)
- **文件**: `services/user_vector_service/vector_processor_service/service.py`
- **主要变更**:
  - 添加输入和输出Stream的初始化
  - 替换队列处理为Stream消息处理
  - 实现单消息处理回调
  - 更新输出队列长度检查

#### vector_writer_service (消费者)
- **文件**: `services/user_vector_service/vector_writer_service/service.py`
- **主要变更**:
  - 添加 `RedisStreamConsumer` 初始化
  - 替换批量处理为单消息处理
  - 更新队列状态API

### 监控和状态检查

#### orc_mongodb_service 状态检查
- **文件**: `services/orc_mongodb_service/status.py`
- **主要变更**:
  - 新增 `check_redis_stream()` 函数
  - 支持Stream长度和消费者组信息查询
  - 更新状态显示格式

#### user_vector_service 监控
- **文件**: `services/user_vector_service/monitoring_service/monitor.py`
- **主要变更**:
  - 更新 `get_queue_status()` 方法查询Stream状态
  - 修改监控显示界面适配Stream信息

## 消息格式

### Stream消息结构
```json
{
  "data": "<JSON序列化的原始消息数据>",
  "timestamp": "<消息时间戳>",
  "service": "<发送服务名称>"
}
```

### 消息确认机制
- 使用Redis Stream的XACK命令确认消息处理完成
- 支持待处理消息查询和重新处理
- 配置最大投递次数和超时时间

## 错误处理和重试

### 重试机制
- **待处理消息超时**: 300秒（可配置）
- **最大投递次数**: 3次（可配置）
- **自动重试**: Stream自动处理消息重试
- **错误恢复**: 消费者重启后自动处理未确认消息

### 监控指标
- Stream长度监控
- 消费者组状态
- 待处理消息数量
- 消费者连接状态

## 兼容性保证

### 配置兼容性
- 保留所有原有配置参数
- 新增Stream配置不影响现有配置
- 支持渐进式迁移

### 功能兼容性
- 消息处理逻辑完全保持不变
- API接口保持兼容
- 监控指标名称适配但功能一致

## 测试验证

### 测试脚本
- **文件**: `test_redis_stream.py`
- **功能**: 验证Redis Stream基本功能和错误处理

### 测试内容
1. Redis连接测试
2. Stream生产者功能测试
3. 消费者组创建和消费测试
4. 消息确认机制测试
5. 错误处理测试

## 部署建议

### 部署步骤
1. 确保Redis版本支持Stream（5.0+）
2. 更新配置文件
3. 重启服务
4. 验证Stream创建和消费者组状态
5. 监控消息处理情况

### 回滚方案
如需回滚到Redis List：
1. 修改配置文件禁用Stream
2. 恢复原有队列处理逻辑
3. 重启服务

## 性能优化

### Stream配置优化
- 合理设置Stream最大长度
- 配置适当的消费者数量
- 调整批量处理大小

### 监控建议
- 监控Stream长度变化
- 关注待处理消息数量
- 监控消费者组健康状态

## 总结

本次迁移成功将两个核心服务从Redis List队列升级到Redis Stream，在保持所有现有功能的同时，提供了更好的消息持久性、重试机制和监控能力。所有修改都经过仔细设计，确保向后兼容性和系统稳定性。
